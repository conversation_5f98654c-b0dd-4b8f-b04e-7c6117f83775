<template>
    <div class="flex" style="min-height: 100vh; background-color: #f0f0f0">
        <!-- 工作台 - 左侧固定 -->
        <div
            class="el-main-left !flex-1"
            style="background-color: #ffffff; min-height: 100vh"
        >
            <div class="el-main-left-top flex justify-between items-center">
                <div class="flex items-center">
                    <!-- <img
                        src="@/assets/images/home/<USER>"
                        alt=""
                        class="w-[30px] object-contain object-center mr-[13px]"
                    /> -->
                    <span class="text-26 font-semibold">工作台</span>
                </div>
            </div>
            <div class="el-main-left-bottom mt-[20px]">
                <component
                    v-if="rightBottomComponent"
                    :is="rightBottomComponent"
                    :key="rightBottomComponentKey"
                    :data="rightBottomData"
                    @update:data="(newData) => (rightBottomData = newData)"
                />
                <div v-else class="w-full flex justify-center mt-[188px]">
                    <div
                        class="w-[144px] h-[110px] flex flex-col items-center justify-center"
                    >
                        <img
                            src="@/assets/admin/file-quesheng.png"
                            class="w-[85px] h-[85px]"
                        />
                        <div
                            class="font18 font-zhongcu"
                            style="color: #666666; width: 10em"
                        >
                            本轮会话暂无内容输出
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 聊天区域 - 右侧固定 -->
        <div
            class="el-main-right !flex-none"
            style="background-color: #ffffff; min-height: 100vh"
        >
            <div class="container container-message !w-[680px]">
                <div class="container-center">
                    <div
                        class="conatiner-message-div flex w-full h-full flex-col justify-between"
                    >
                        <div
                            ref="scrollContainer"
                            class="taskmessage-div overflow-y"
                            @scroll="handleScroll"
                        >
                            <div ref="innerRef">
                                <!-- 应用的开场白 -->
                                <div
                                    class="opening-statement-div"
                                    v-if="taskmesssageList.length <= 0"
                                >
                                    <div>{{ displayedText1 }}</div>
                                    <div>{{ displayedText2 }}</div>
                                </div>
                                <!-- 聊天内容 -->
                                <div v-else class="mt-[65px]">
                                    <template
                                        v-for="(
                                            message, index
                                        ) in taskmesssageList"
                                        :key="index"
                                    >
                                        <template
                                            v-if="message.role === 'user'"
                                        >
                                            <div
                                                class="border-0 border-b border-[#ECECF2] border-solid pb-[20px]"
                                            >
                                                <div class="flex items-center">
                                                    <div
                                                        class="text-26 font-zhongcu leading-30 whitespace-pre-wrap markdown-content user-message max-w-[96%]"
                                                        v-html="
                                                            renderMarkdown(
                                                                message.content
                                                            )
                                                        "
                                                    ></div>
                                                    <template
                                                        v-if="
                                                            message.favorite
                                                                ? message
                                                                      .favorite
                                                                      .id
                                                                : false
                                                        "
                                                    >
                                                        <el-tooltip
                                                            class="box-item"
                                                            effect="dark"
                                                            content="取消收藏"
                                                            placement="bottom"
                                                        >
                                                            <el-button
                                                                type=""
                                                                size="small"
                                                                text
                                                                @click="
                                                                    handleFavorite(
                                                                        message,
                                                                        '0'
                                                                    )
                                                                "
                                                                class="flex-shrink-0 w-[20px] h-[20px] ml-1"
                                                                :disabled="
                                                                    message.favoriteLoading
                                                                "
                                                            >
                                                                <template
                                                                    v-if="
                                                                        message.favoriteLoading
                                                                    "
                                                                >
                                                                    <el-icon
                                                                        class="is-loading"
                                                                        ><Loading
                                                                    /></el-icon>
                                                                </template>
                                                                <template
                                                                    v-else
                                                                >
                                                                    <CollectActiveIcon
                                                                        size="20"
                                                                    />
                                                                </template>
                                                            </el-button>
                                                        </el-tooltip>
                                                    </template>

                                                    <template v-else>
                                                        <template
                                                            v-if="
                                                                index !==
                                                                taskmesssageList.length -
                                                                    2
                                                            "
                                                        >
                                                            <el-tooltip
                                                                class="box-item"
                                                                effect="dark"
                                                                content="收藏指令"
                                                                placement="bottom"
                                                            >
                                                                <el-button
                                                                    type=""
                                                                    size="small"
                                                                    text
                                                                    @click="
                                                                        handleFavorite(
                                                                            message,
                                                                            '1'
                                                                        )
                                                                    "
                                                                    class="flex-shrink-0 w-[20px] h-[20px] ml-1"
                                                                    :disabled="
                                                                        message.favoriteLoading
                                                                    "
                                                                >
                                                                    <template
                                                                        v-if="
                                                                            message.favoriteLoading
                                                                        "
                                                                    >
                                                                        <el-icon
                                                                            class="is-loading"
                                                                            ><Loading
                                                                        /></el-icon>
                                                                    </template>
                                                                    <template
                                                                        v-else
                                                                    >
                                                                        <CollectIcon
                                                                            size="20"
                                                                        />
                                                                    </template>
                                                                </el-button>
                                                            </el-tooltip>
                                                        </template>
                                                        <template v-else>
                                                            <template
                                                                v-if="
                                                                    showOperate
                                                                "
                                                            >
                                                                <el-tooltip
                                                                    class="box-item"
                                                                    effect="dark"
                                                                    content="收藏指令"
                                                                    placement="bottom"
                                                                >
                                                                    <el-button
                                                                        type=""
                                                                        size="small"
                                                                        text
                                                                        @click="
                                                                            handleFavorite(
                                                                                message,
                                                                                '1'
                                                                            )
                                                                        "
                                                                        class="flex-shrink-0 w-[20px] h-[20px] ml-1"
                                                                        :disabled="
                                                                            message.favoriteLoading
                                                                        "
                                                                    >
                                                                        <template
                                                                            v-if="
                                                                                message.favoriteLoading
                                                                            "
                                                                        >
                                                                            <el-icon
                                                                                class="is-loading"
                                                                                ><Loading
                                                                            /></el-icon>
                                                                        </template>
                                                                        <template
                                                                            v-else
                                                                        >
                                                                            <CollectIcon
                                                                                size="20"
                                                                            />
                                                                        </template>
                                                                    </el-button>
                                                                </el-tooltip>
                                                            </template>
                                                        </template>
                                                    </template>
                                                </div>

                                                <div
                                                    class="text-base text-[#909090] mt-[11px]"
                                                    v-if="message.create_time"
                                                >
                                                    {{ message.create_time }}
                                                </div>
                                            </div>
                                        </template>

                                        <template
                                            v-else-if="
                                                message.role === 'assistant'
                                            "
                                        >
                                            <div class="mb-[50px]">
                                                <div
                                                    class="flex items-center text-lg font-zhongcu mt-[20px] mb-[10px]"
                                                >
                                                    <img
                                                        src="@/assets/images/home/<USER>"
                                                        alt=""
                                                        class="w-[34px] h-[34px] mr-[13px]"
                                                    />回答
                                                </div>
                                                <div class="text-lg">
                                                    <component
                                                        v-if="
                                                            message.topComponent
                                                        "
                                                        :is="
                                                            message.topComponent
                                                        "
                                                        :key="
                                                            message.topComponentKey
                                                        "
                                                        :data="message.topData"
                                                        @update:data="
                                                            (newData) =>
                                                                (message.topData =
                                                                    newData)
                                                        "
                                                    />
                                                    <ThinkingBox
                                                        v-if="
                                                            message.thinkingContent
                                                        "
                                                        :content="
                                                            message.thinkingContent
                                                        "
                                                        :completed="
                                                            message.thinkingCompleted
                                                        "
                                                    />
                                                    <div
                                                        class="mt-[20px] leading-30 text-base"
                                                        v-html="
                                                            renderMarkdown(
                                                                message.content
                                                            )
                                                        "
                                                    ></div>
                                                    <!-- 新增 mid-txt-bottom 区域组件 -->
                                                    <div
                                                        class="mt-[0px]"
                                                        v-if="
                                                            message.txtBottomComponent
                                                        "
                                                    >
                                                        <component
                                                            :is="
                                                                message.txtBottomComponent
                                                            "
                                                            :key="
                                                                message.txtBottomComponentKey
                                                            "
                                                            :data="
                                                                message.txtBottomData
                                                            "
                                                            @update:data="
                                                                (newData) =>
                                                                    (message.txtBottomData =
                                                                        newData)
                                                            "
                                                        />
                                                    </div>
                                                </div>
                                                <img
                                                    src="@/assets/images/chat/loading.gif"
                                                    alt=""
                                                    class="h-[8px]"
                                                    v-if="
                                                        showLoading &&
                                                        index ==
                                                            taskmesssageList.length -
                                                                1
                                                    "
                                                />
                                                <div
                                                    class="border-0 border-b border-[#ECECF2] border-solid pb-[15px] mt-[25px] flex justify-end items-center"
                                                    v-if="
                                                        index !==
                                                            taskmesssageList.length -
                                                                1 || showOperate
                                                    "
                                                >
                                                    <el-tooltip
                                                        effect="dark"
                                                        content="复制"
                                                        placement="bottom"
                                                    >
                                                        <button
                                                            class="p-0 border-none bg-transparent"
                                                            @click="
                                                                copyContent(
                                                                    message.content
                                                                )
                                                            "
                                                        >
                                                            <img
                                                                src="@/assets/images/home/<USER>/<EMAIL>"
                                                                alt=""
                                                                class="w-[20px] h-[20px] object-contain object-center"
                                                            />
                                                        </button>
                                                    </el-tooltip>
                                                    <el-tooltip
                                                        effect="dark"
                                                        content="重新生成"
                                                        placement="bottom"
                                                        v-if="
                                                            message.role ===
                                                                'assistant' &&
                                                            index ===
                                                                taskmesssageList.length -
                                                                    1
                                                        "
                                                    >
                                                        <button
                                                            class="p-0 border-none bg-transparent ml-5"
                                                            @click="
                                                                againSend(
                                                                    message.taskmessage_id
                                                                )
                                                            "
                                                        >
                                                            <img
                                                                src="@/assets/images/home/<USER>/refresh.png"
                                                                alt=""
                                                                class="w-[20px] h-[20px] object-contain object-center"
                                                            />
                                                        </button>
                                                    </el-tooltip>
                                                    <div
                                                        class="w-[1px] h-[20px] bg-[#121619] ml-5"
                                                    ></div>
                                                    <el-tooltip
                                                        effect="dark"
                                                        content="赞"
                                                        placement="bottom"
                                                    >
                                                        <button
                                                            class="p-0 border-none bg-transparent ml-5 w-[20px] h-[20px]"
                                                            @click="
                                                                delCommentVote(
                                                                    message
                                                                )
                                                            "
                                                            v-if="
                                                                message.message_comment
                                                                    ? message
                                                                          .message_comment
                                                                          .vote ==
                                                                      '1'
                                                                    : false
                                                            "
                                                        >
                                                            <LikeActiveIcon
                                                                size="20"
                                                            />
                                                        </button>
                                                        <button
                                                            class="p-0 border-none bg-transparent ml-5 w-[20px] h-[20px]"
                                                            @click="
                                                                sendCommentVote(
                                                                    message,
                                                                    '1'
                                                                )
                                                            "
                                                            v-else
                                                        >
                                                            <LikeIcon
                                                                color="#121619"
                                                                size="20"
                                                                style="
                                                                    width: 20px;
                                                                    height: 20px;
                                                                "
                                                            />
                                                        </button>
                                                    </el-tooltip>
                                                    <el-tooltip
                                                        effect="dark"
                                                        content="踩"
                                                        placement="bottom"
                                                    >
                                                        <button
                                                            class="p-0 border-none bg-transparent ml-5 w-[20px] h-[20px]"
                                                            @click="
                                                                delCommentVote(
                                                                    message
                                                                )
                                                            "
                                                            v-if="
                                                                message.message_comment
                                                                    ? message
                                                                          .message_comment
                                                                          .vote ==
                                                                      '0'
                                                                    : false
                                                            "
                                                        >
                                                            <NoLikeActiveIcon
                                                                size="21"
                                                            />
                                                        </button>
                                                        <button
                                                            class="p-0 border-none bg-transparent ml-5 w-[20px] h-[20px]"
                                                            @click="
                                                                sendCommentVote(
                                                                    message,
                                                                    '0'
                                                                )
                                                            "
                                                            v-else
                                                        >
                                                            <NoLikeIcon
                                                                color="#121619"
                                                                size="20"
                                                            />
                                                        </button>
                                                    </el-tooltip>
                                                </div>
                                                <div
                                                    class="mt-[15px] flex items-center flex-wrap"
                                                    v-if="
                                                        message.bottomComponent
                                                    "
                                                >
                                                    <component
                                                        :is="
                                                            message.bottomComponent
                                                        "
                                                        :key="
                                                            message.bottomComponentKey
                                                        "
                                                        :data="
                                                            message.bottomData
                                                        "
                                                        @update:data="
                                                            (newData) =>
                                                                (message.bottomData =
                                                                    newData)
                                                        "
                                                    />
                                                </div>
                                            </div>
                                        </template>
                                    </template>
                                </div>
                            </div>
                        </div>
                        <!-- <div class="taskmessage-add">
                            <div
                                :style="{
                                    borderColor: focusIpt ? '' : '#d9d9d9',
                                }"
                            >
                                <div class="flex flex-1 items-center">
                                    <el-input
                                        v-model="userContent"
                                        :autosize="{ minRows: 3, maxRows: 6 }"
                                        type="textarea"
                                        placeholder="输入问题"
                                        resize="none"
                                        :disabled="!canWriteIpt"
                                        maxlength="20000"
                                        @keydown="handleKeyDown"
                                        @focus="focusIpt = true"
                                        ref="inputTxtRef"
                                    />
                                </div>

                                <div class="flex items-end justify-between">
                                    <div>模型管理</div>
                                    <button
                                        :class="
                                            canSendMessage ? '' : 'send-btn-no'
                                        "
                                        :disabled="!canSendMessage"
                                        @click="sendTaskmessageHandler"
                                        class="flex items-center justify-center"
                                    >
                                        <img
                                            src="@/assets/images/home/<USER>/<EMAIL>"
                                            alt=""
                                        />
                                    </button>
                                    <el-tooltip
                                        effect="dark"
                                        content="停止"
                                        placement="top"
                                    >
                                        <button
                                            class="p-0 border-none"
                                            style="background: none"
                                        >
                                            <StopChatIcon />
                                        </button>
                                    </el-tooltip>
                                </div>
                            </div>
                        </div> -->
                        <div
                            class="chat-input-box"
                            :style="{ borderColor: focusIpt ? '' : '#d9d9d9' }"
                        >
                            <div class="flex flex-1 items-center">
                                <el-input
                                    v-model="userContent"
                                    :autosize="{ minRows: 1, maxRows: 6 }"
                                    type="textarea"
                                    placeholder="输入问题"
                                    resize="none"
                                    :disabled="!canWriteIpt"
                                    maxlength="20000"
                                    @keydown="handleKeyDown"
                                    @focus="focusIpt = true"
                                    ref="inputTxtRef"
                                />
                            </div>
                            <div class="flex items-end">
                                <div class="h-[52px] flex items-center">
                                    <button
                                        :class="
                                            canSendMessage ? '' : 'send-btn-no'
                                        "
                                        :disabled="!canSendMessage"
                                        @click="sendTaskmessageHandler"
                                        class="flex items-center justify-center"
                                    >
                                        <img
                                            src="@/assets/images/home/<USER>/<EMAIL>"
                                            alt=""
                                        />
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="conatiner-tips-div font14">
                    内容由AI生成，无法确保真实准确，仅供参考，请阅读并遵守《AiALIGN用户协议》
                </div>
            </div>
        </div>
    </div>
</template>
<script setup name="ChatView">
import {
    onMounted,
    ref,
    watch,
    nextTick,
    onUnmounted,
    computed,
    shallowRef,
} from 'vue';
import { useRoute, useRouter } from 'vue-router';
import hljs from 'highlight.js';
import 'highlight.js/styles/atom-one-dark.css'; // 选择你喜欢的样式
import { ElMessage, ElLoading } from 'element-plus';
import { marked } from 'marked';
import { StreamMsgType } from '@/services/types/msg.ts';
import { getCurrentTime } from './utils';
import { encodeBase64AndUrl } from '@/utils/encoding';
import {
    sendTaskmessage,
    getTaskmessages,
    delTaskmessageApi,
    addMessageCommentApi,
    delMessageCommentApi,
    addFavoriteApi,
    delFavoriteApi,
} from '@/services/intellido';

import { usePreviewComponents } from './hooks/usePreviewComponents';
import { resolveComponent } from './components/componentRegister';
import { useThinking } from './hooks/useThinking'; // 引入思考钩子

import ThinkingBox from './components/ThinkingBox.vue'; // 引入思考框组件

import { useUserStore } from '@/store/modules/userInfo';

import { useMessageBus, MessageType } from './hooks/useMessageBus';
import RefreshIcon from '@/components/icons/RefreshIcon.vue';
import LikeIcon from '@/components/icons/chat/LikeIcon.vue';
import NoLikeIcon from '@/components/icons/chat/NoLikeIcon.vue';
import LikeActiveIcon from '@/components/icons/chat/LikeActiveIcon.vue';
import NoLikeActiveIcon from '@/components/icons/chat/NoLikeActiveIcon.vue';
import { debounce } from 'lodash-es';
import CollectIcon from '@/components/icons/favorite/CollectIcon.vue';
import CollectActiveIcon from '@/components/icons/favorite/CollectActiveIcon.vue';
import StopChatIcon from '@/components/icons/chat/StopChatIcon.vue';
import { Loading } from '@element-plus/icons-vue';

const userStore = useUserStore();
// 使用 computed 来确保响应性
const userInfo = computed(() => userStore.userInfo);

const route = useRoute();
const router = useRouter();
const canSendMessage = ref(false); //能否发送消息
const focusIpt = ref(false); //是否聚焦输入框
const userContent = ref(''); // 用户输入的内容
const canWriteIpt = ref(true); //默认可以输入内容
const taskmesssageList = ref([]); //上下文列表
const scrollContainer = ref(null); //滚动条滚动高度
const innerRef = ref(null);
const displayedText1 = ref('');
let currentIndex1 = 0;
let cursorInterval1 = null;
const displayedText2 = ref('');
let currentIndex2 = 0;
let cursorInterval2 = null;
const selectedAppId = ref(route.query.app_id || null); //默认app的id
const selectQueryAppOpening = ref(null); //选中应用的开场白
const selectedTopicId = ref(route.query.topic_id || null); //默认选中的第一个上下文的id
const stream = ref(1); //发起聊天时的请求模式   0正常请求    1流模式请求
const showLoading = ref(false);
const showTool = ref(false);
const displayedTextLoading = ref(''); //等待ai回复时展示的文案
const showOperate = ref(true); //是否展示操作按钮
const markdownCache = ref(''); // 缓存不完整的Markdown数据
const curPreview = ref(); // 当前预览
const isRendering = ref(false); //ai内容是否还在渲染中
const inputTxtRef = ref(null);
const canScrollToBottom = ref(true); //ai回复内容时是否执行scrollToBottom函数，让内容一直至于底部
const jsonCache = ref(''); // 添加缓存存储不完整的JSON字符串

const inputMsgQuery = route.query.question; //从其他页面带过来的用户输入的内容
const inputMsg = ref('');
if (inputMsgQuery) {
    try {
        inputMsg.value = decodeURIComponent(inputMsgQuery);
    } catch (e) {
        debugLogger.error('URL解码失败:', e);
        inputMsg.value = inputMsgQuery; // 如果解码失败，使用原始值
    }
}

if (route.query.app_id) {
    selectedAppId.value = route.query.app_id;
}

// 设置页面标题
const setPageTitle = (title) => {
    document.title = title;
};

// 如果需要在 Chat 页面中设置标题，使用新的编码方式
const updateTitle = (title) => {
    const encodedTitle = encodeBase64AndUrl(title);
    router.replace({
        query: { ...route.query, _t: encodedTitle },
    });
};

// 初始化思考钩子
const {
    processText,
    getThinkingContent,
    hasThinkingContent,
    resetThinking,
    isThinkingCompleted,
} = useThinking();

// 监听 taskmesssageList 的变化，并在变化后进行高亮处理
watch(taskmesssageList, async () => {
    // 初始化工作台
    for (let i = taskmesssageList.value.length - 1; i >= 0; i--) {
        const att_data = taskmesssageList.value[i].attachments_data;
        if (att_data && att_data.length > 0) {
            curPreview.value = att_data[att_data.length - 1];
            break;
        }
    }
    await nextTick();
    document.querySelectorAll('pre code').forEach((block) => {
        if (!block.dataset.highlighted) {
            hljs.highlightElement(block);
            block.dataset.highlighted = 'yes';
        }
    });
});

const showTitle1 = async () => {
    let fullText = `Hello，${userInfo.value.nickname}`;
    if (currentIndex1 < fullText.length) {
        displayedText1.value += fullText[currentIndex1];
        currentIndex1++;
        setTimeout(showTitle1, 100); // 调整速度
    } else {
        showTitle2();
        clearInterval(cursorInterval1);
    }
};

const showTitle2 = () => {
    let fullText = '';
    if (selectQueryAppOpening.value) {
        fullText = selectQueryAppOpening.value;
    } else {
        fullText = `很高兴见到你，开始探索吧！`;
    }
    if (currentIndex2 < fullText.length) {
        displayedText2.value += fullText[currentIndex2];
        currentIndex2++;
        setTimeout(showTitle2, 100); // 调整速度
    } else {
        clearInterval(cursorInterval2);
    }
};

// 配置 marked 以使用 highlight.js
marked.setOptions({
    highlight: function (code, lang) {
        if (hljs.getLanguage(lang)) {
            return hljs.highlight(code, { language: lang }).value;
        } else {
            return hljs.highlightAuto(code).value;
        }
    },
});

// 渲染Markdown内容
const renderMarkdown = (content) => {
    // debugger;
    if (typeof content === 'object') {
        return marked(JSON.stringify(content, null, 2));
    }
    return marked(String(content));
};

// 添加调试工具类
const debugLogger = {
    enabled: false, // 默认关闭调试日志
    log(...args) {
        if (this.enabled) {
            console.log('[Stream]', ...args);
        }
    },
    error(...args) {
        if (this.enabled) {
            console.error('[Stream]', ...args);
        }
    },
};

// 可以通过以下方式开启/关闭调试日志
// debugLogger.enabled = true;

const parseStreamToJson = (dataStr) => {
    jsonCache.value += dataStr;

    const tryParseJson = (str) => {
        try {
            return JSON.parse(str);
        } catch (e) {
            return null;
        }
    };

    if (jsonCache.value.includes('}{')) {
        const parts = jsonCache.value.split('}{');
        const result = [];

        for (let i = 0; i < parts.length; i++) {
            let processedPart = parts[i];
            if (i === 0) {
                processedPart = processedPart + '}';
            } else if (i === parts.length - 1) {
                processedPart = '{' + processedPart;
            } else {
                processedPart = '{' + processedPart + '}';
            }

            const parsedJson = tryParseJson(processedPart);
            if (parsedJson) {
                result.push(parsedJson);
                if (i === parts.length - 1) {
                    jsonCache.value = '';
                }
            } else {
                debugLogger.log('JSON解析失败，保留在缓存中:', processedPart);
                if (i === parts.length - 1) {
                    jsonCache.value = processedPart;
                }
            }
        }

        return result.length > 0 ? result : [];
    } else {
        const parsedJson = tryParseJson(jsonCache.value);
        if (parsedJson) {
            jsonCache.value = '';
            return [parsedJson];
        }
        debugLogger.log('JSON解析失败，保留在缓存中:', jsonCache.value);
        return [];
    }
};

// 解析流数据
const parseStreamData = (orgData) => {
    try {
        // debugger;
        debugLogger.log('原始数据:', orgData);
        const itemList = parseStreamToJson(orgData);
        debugLogger.log('JSON解析后的数据:', itemList);
        const res = itemList
            .map((item) => {
                try {
                    const data = item;
                    if (data.error) {
                        debugLogger.log('发现错误数据:', data.error);
                        return {
                            topic_id: null,
                            content: [{ content: '' }],
                            taskmessage_id: '',
                            error: data.error,
                        };
                    } else if (data.topic_id) {
                        debugLogger.log('发现topic数据:', data);
                        const topic_id = data.topic_id;
                        selectedTopicId.value = topic_id;
                        const content = data.data || '';
                        const taskmessage_id =
                            data.taskmessage_id || data.message_id;
                        return {
                            topic_id,
                            content: [{ content }],
                            taskmessage_id,
                        };
                    } else {
                        if (data.uid === 'file-list-component') {
                            debugLogger.log('发现文件列表数据:', data);
                        }
                        data.content = data.data;
                        return data;
                    }
                } catch (error) {
                    debugLogger.error('解析单条消息失败:', error);
                    return null;
                }
            })
            .filter(Boolean);

        debugLogger.log('最终解析结果:', {
            数据条数: res.length,
            是否包含错误: res.length === 1 && res[0].error,
            是否包含Topic:
                res.length === 1 && typeof res[0].topic_id === 'number',
        });

        if (res.length === 1 && res[0].error) {
            return res[0];
        } else if (res.length === 1 && typeof res[0].topic_id === 'number') {
            return res[0];
        } else {
            // 检查res数组中是否有包含message_id和topic_id的数据
            const messageItem = res.find(
                (item) => item.taskmessage_id && item.topic_id
            );
            if (messageItem) {
                return {
                    topic_id: messageItem.topic_id,
                    taskmessage_id: messageItem.taskmessage_id,
                    content: res,
                };
            }
            return { topic_id: null, content: res };
        }
    } catch (error) {
        debugLogger.error('解析流数据失败:', error);
        return {
            topic_id: null,
            content: [{ content: '' }],
            taskmessage_id: '',
        };
    }
};

// 监听键盘事件
const handleKeyDown = (event) => {
    // 检查是否按下了Enter键
    if (event.key === 'Enter') {
        // 检查userContent是否为空
        if (userContent.value.trim() === '') {
            // 如果userContent为空，阻止默认行为
            event.preventDefault();
            return; // 不继续执行
        }
        // 检查是否同时按下了Shift键
        if (event.shiftKey || event.ctrlKey) {
            // 如果按下了Shift+Enter或ctrl+enter，执行换行操作
            userContent.value += '\n';
            event.preventDefault(); // 阻止默认的换行行为
        } else {
            // 如果只按下了Enter，发送文本
            sendTaskmessageHandler();
        }
    }
};

// 清空工作台
const clearWorkbench = () => {
    rightBottomComponent.value = null;
};

import { mockData, getAnswer } from './mock';
// 发起聊天函数
const sendTaskmessageHandler = async () => {
    canScrollToBottom.value = true; //可以执行scrollToBottom函数
    const userMessage = userContent.value;
    const userMessageObj = {
        role: 'user',
        content: userMessage,
        taskmessage_id: '',
        create_time: getCurrentTime(),
        favorite: null,
    };
    taskmesssageList.value.push(userMessageObj);
    // 清空缓存和状态
    markdownCache.value = '';
    resetThinking(); // 重置所有思考状态

    let aiMessage = {
        role: 'assistant',
        content: '',
        taskmessage_id: '',
        thinkingContent: '', // 添加思考内容字段
        thinkingCompleted: false, // 添加 thinkingCompleted 字段
        message_comment: {},
    };
    taskmesssageList.value.push(aiMessage);
    scrollToBottom();
    showTool.value = false;
    userContent.value = ''; // 清空输入框
    showLoading.value = true;
    showOperate.value = false; //将最后一条聊天内容的操作按钮隐藏，等聊天内容全展示后，在将按钮展示
    canWriteIpt.value = false; //不能输入内容
    isRendering.value = true;
    clearWorkbench(); //清空工作台
    focusIpt.value = false; //失去焦点
    try {
        // 检查是否命中 mock 数据
        const mockAnswer = getAnswer(userMessage);
        if (mockAnswer) {
            // 模拟异步响应延迟
            await new Promise((resolve) => setTimeout(resolve, 2000));

            // 为模拟消息创建唯一ID
            const mockTaskMessageId = `mock-${Date.now()}`;
            // 重置思考状态
            resetThinking(mockTaskMessageId);

            if (Array.isArray(mockAnswer)) {
                // 处理包含组件的 mock 响应
                mockAnswer.forEach((item) => {
                    if (item.uid === 'default') {
                        // 处理普通文本消息，提取思考内容
                        const result = processText(
                            item.data || '',
                            mockTaskMessageId
                        );
                        aiMessage.content += result.normalText;

                        // 更新思考内容
                        if (hasThinkingContent(mockTaskMessageId)) {
                            aiMessage.thinkingContent =
                                getThinkingContent(mockTaskMessageId);
                            aiMessage.thinkingCompleted =
                                isThinkingCompleted(mockTaskMessageId);
                        }
                    } else if (!item.topic_id) {
                        // 更新预览组件
                        handlePreviewMessage(item);
                    }
                });
            } else {
                // 处理纯文本 mock 响应，同样提取思考内容
                const result = processText(mockAnswer, mockTaskMessageId);
                aiMessage.content = result.normalText;

                // 更新思考内容
                if (hasThinkingContent(mockTaskMessageId)) {
                    aiMessage.thinkingContent =
                        getThinkingContent(mockTaskMessageId);
                    aiMessage.thinkingCompleted =
                        isThinkingCompleted(mockTaskMessageId);
                }
            }

            // 生成一个模拟的 taskmessage_id
            aiMessage.taskmessage_id = mockTaskMessageId;
            userMessageObj.taskmessage_id = mockTaskMessageId;

            // 更新消息列表
            taskmesssageList.value = [...taskmesssageList.value];

            // 重置状态
            showLoading.value = false;
            canWriteIpt.value = true;
            showOperate.value = true;
            isRendering.value = false;

            scrollToBottom();
            return;
        } else {
            const response = await sendTaskmessage(
                userMessage,
                selectedAppId.value,
                selectedTopicId.value,
                selectedAppId.value ? '' : 'default'
            );
            if (stream.value == 1) {
                // 检查 response 是否包含流数据
                if (response && response.getReader) {
                    const reader = response.getReader();
                    const decoder = new TextDecoder();
                    let done = false;
                    // 处理流数据
                    const processStream = async () => {
                        while (!done) {
                            const { value, done: streamDone } =
                                await reader.read();
                            done = streamDone;
                            if (value) {
                                const chunk = decoder.decode(value, {
                                    stream: true,
                                });
                                debugLogger.log('[Stream] 收到新的数据块');

                                let cacheTaskmessageId = '';

                                const { content, taskmessage_id, error } =
                                    parseStreamData(chunk);

                                if (taskmessage_id) {
                                    cacheTaskmessageId = taskmessage_id;
                                }

                                debugLogger.log('[Stream] 解析后的数据结构:', {
                                    content: content,
                                    hasFileList:
                                        content.some &&
                                        content.some(
                                            (item) =>
                                                item.uid ===
                                                'file-list-component'
                                        ),
                                    taskmessage_id: taskmessage_id,
                                    error: error,
                                });

                                if (error == '1') {
                                    ElMessage.error('应用不存在');
                                    showLoading.value = false;
                                    return;
                                }

                                let text = '';
                                content.map((item) => {
                                    debugLogger.log('[Stream] 处理单条数据:', {
                                        type: item.type,
                                        uid: item.uid,
                                        topic_id: item.topic_id,
                                        area: item.area,
                                    });

                                    if (item.type == StreamMsgType.StartTool) {
                                        showLoading.value = true;
                                        showTool.value = true;
                                        displayedTextLoading.value =
                                            item.content;
                                    } else if (item.uid !== 'default') {
                                        if (!item.topic_id) {
                                            debugLogger.log(
                                                '[Stream] 准备处理非默认消息:',
                                                item
                                            );
                                            handlePreviewMessage(item);
                                        }
                                    } else {
                                        // 处理思考中文本
                                        const messageId =
                                            aiMessage.taskmessage_id;
                                        // debugger;
                                        // 处理文本，提取思考内容
                                        const result = processText(
                                            item.data || '',
                                            messageId
                                        );
                                        text += result.normalText;

                                        // 更新思考内容
                                        if (hasThinkingContent(messageId)) {
                                            aiMessage.thinkingContent =
                                                getThinkingContent(messageId);
                                            aiMessage.thinkingCompleted =
                                                isThinkingCompleted(messageId);
                                        }
                                    }
                                });

                                // 缓存不完整的Markdown数据
                                markdownCache.value += text;
                                try {
                                    // 尝试渲染Markdown
                                    aiMessage.content = markdownCache.value;
                                    if (taskmessage_id) {
                                        console.log(
                                            taskmessage_id,
                                            '====================='
                                        );
                                        aiMessage.taskmessage_id =
                                            taskmessage_id;
                                        // 更新用户消息的 taskmessage_id
                                        userMessageObj.taskmessage_id =
                                            taskmessage_id;
                                    }
                                    // 强制 Vue 重新渲染
                                    taskmesssageList.value = [
                                        ...taskmesssageList.value,
                                    ];
                                    scrollToBottom();
                                } catch (error) {
                                    // 如果渲染失败，继续缓存
                                }
                            }
                        }
                        showLoading.value = false;
                        canWriteIpt.value = true;

                        inputTxtRef.value.focus();
                        scrollToBottom();
                        taskmesssageList.value = [...taskmesssageList.value];

                        // 流数据处理完成后，设置 showOperate 为 true
                        showOperate.value = true;
                        isRendering.value = false; //ai内容全部渲染完成
                    };

                    processStream().catch((error) => {
                        debugLogger.error('处理流数据失败:', error);
                        showLoading.value = false;
                        canWriteIpt.value = true;
                        taskmesssageList.value.pop();
                        taskmesssageList.value.push({
                            role: 'assistant',
                            content: '当前请求网络可能有问题，请重新发起对话',
                        });
                        scrollToBottom();
                    });
                } else {
                    throw new Error('Invalid response format for stream mode');
                }
            } else {
                canWriteIpt.value = true;
                // 正常请求
                if (response.data.error == '1') {
                    ElMessage.error('应用不存在');
                } else if (response.data.error == '0') {
                    // 等待中消失
                    showLoading.value = false;
                    isRendering.value = false; //ai内容全部渲染完成
                    const { topic_id, content, taskmessage_id } = response.data;
                    taskmesssageList.value.push({
                        role: 'assistant',
                        content: content,
                        taskmessage_id: taskmessage_id,
                    });
                    selectedTopicId.value = topic_id;
                    // 更新用户消息的 taskmessage_id
                    userMessageObj.taskmessage_id = taskmessage_id;
                    scrollToBottom();
                    // 可以展示操作按钮
                    showOperate.value = true;
                }
            }
        }
    } catch (error) {
        debugLogger.error('发送消息失败:', error);
        showLoading.value = false;
        canWriteIpt.value = true;
        taskmesssageList.value.pop();
        taskmesssageList.value.push({
            role: 'assistant',
            content: '当前请求网络可能有问题，请重新发起对话',
        });
        scrollToBottom();
    }
};

// 重新生成
const againSend = async (taskmessage_id) => {
    const response = await delTaskmessageApi(
        // selectedQueryAppId.value,
        // selectedTopicId.value,
        taskmessage_id
    );
    if (response.data.error == '0') {
        // 只删除AI回复内容
        taskmesssageList.value = taskmesssageList.value.filter(
            (item) =>
                !(
                    item.taskmessage_id === taskmessage_id &&
                    item.role === 'assistant'
                )
        );

        // 获取对应的用户消息
        const userMessageObj = taskmesssageList.value.find(
            (item) =>
                item.taskmessage_id === taskmessage_id && item.role === 'user'
        );

        // 清空缓存和状态
        markdownCache.value = '';
        resetThinking(); // 重置所有思考状态

        let aiMessage = {
            role: 'assistant',
            content: '',
            taskmessage_id: '',
            thinkingContent: '', // 添加思考内容字段
            thinkingCompleted: false, // 添加 thinkingCompleted 字段
            message_comment: {},
        };
        taskmesssageList.value.push(aiMessage);
        scrollToBottom();
        showTool.value = false;
        userContent.value = ''; // 清空输入框
        showLoading.value = true;
        showOperate.value = false; //将最后一条聊天内容的操作按钮隐藏，等聊天内容全展示后，在将按钮展示
        canWriteIpt.value = false; //不能输入内容
        isRendering.value = true;
        showPreview.value = false; //工作台隐藏预览
        clearWorkbench(); //清空工作台
        focusIpt.value = false; //失去焦点

        if (userMessageObj) {
            try {
                const response = await sendTaskmessage(
                    userMessageObj.content,
                    selectedAppId.value,
                    selectedTopicId.value,
                    selectedAppId.value ? '' : 'default'
                );

                scrollToBottom();
                if (stream.value == 1) {
                    // 检查 response 是否包含流数据
                    if (response && response.getReader) {
                        const reader = response.getReader();
                        const decoder = new TextDecoder();
                        let done = false;

                        // 处理流数据
                        const processStream = async () => {
                            while (!done) {
                                const { value, done: streamDone } =
                                    await reader.read();
                                done = streamDone;
                                if (value) {
                                    const chunk = decoder.decode(value, {
                                        stream: true,
                                    });
                                    debugLogger.log('[Stream] 收到新的数据块');

                                    let cacheTaskmessageId = '';

                                    const { content, taskmessage_id, error } =
                                        parseStreamData(chunk);

                                    if (taskmessage_id) {
                                        cacheTaskmessageId = taskmessage_id;
                                    }

                                    debugLogger.log(
                                        '[Stream] 解析后的数据结构:',
                                        {
                                            content: content,
                                            hasFileList:
                                                content.some &&
                                                content.some(
                                                    (item) =>
                                                        item.uid ===
                                                        'file-list-component'
                                                ),
                                            taskmessage_id: taskmessage_id,
                                            error: error,
                                        }
                                    );

                                    if (error == '1') {
                                        ElMessage.error('应用不存在');
                                        showLoading.value = false;
                                        return;
                                    }

                                    let text = '';
                                    content.map((item) => {
                                        debugLogger.log(
                                            '[Stream] 处理单条数据:',
                                            {
                                                type: item.type,
                                                uid: item.uid,
                                                topic_id: item.topic_id,
                                                area: item.area,
                                            }
                                        );

                                        if (
                                            item.type == StreamMsgType.StartTool
                                        ) {
                                            showLoading.value = true;
                                            showTool.value = true;
                                            displayedTextLoading.value =
                                                item.content;
                                        } else if (item.uid !== 'default') {
                                            if (!item.topic_id) {
                                                debugLogger.log(
                                                    '[Stream] 准备处理非默认消息:',
                                                    item
                                                );
                                                handlePreviewMessage(item);
                                            }
                                        } else {
                                            // 处理思考中文本
                                            const messageId =
                                                aiMessage.taskmessage_id;
                                            // debugger;
                                            // 处理文本，提取思考内容
                                            const result = processText(
                                                item.data || '',
                                                messageId
                                            );
                                            text += result.normalText;

                                            // 更新思考内容
                                            if (hasThinkingContent(messageId)) {
                                                aiMessage.thinkingContent =
                                                    getThinkingContent(
                                                        messageId
                                                    );
                                                aiMessage.thinkingCompleted =
                                                    isThinkingCompleted(
                                                        messageId
                                                    );
                                            }
                                        }
                                    });

                                    // 缓存不完整的Markdown数据
                                    markdownCache.value += text;
                                    try {
                                        // 尝试渲染Markdown
                                        aiMessage.content = markdownCache.value;
                                        if (taskmessage_id) {
                                            aiMessage.taskmessage_id =
                                                taskmessage_id;
                                            // 更新用户消息的 taskmessage_id
                                            userMessageObj.taskmessage_id =
                                                taskmessage_id;
                                        }
                                        // 强制 Vue 重新渲染
                                        taskmesssageList.value = [
                                            ...taskmesssageList.value,
                                        ];
                                        // console.log(taskmesssageList.value);
                                        scrollToBottom();
                                    } catch (error) {
                                        // 如果渲染失败，继续缓存
                                    }
                                }
                            }
                            showLoading.value = false;
                            canWriteIpt.value = true;

                            inputTxtRef.value.focus();
                            scrollToBottom();
                            taskmesssageList.value = [
                                ...taskmesssageList.value,
                            ];

                            // 流数据处理完成后，设置 showOperate 为 true
                            showOperate.value = true;
                            isRendering.value = false; //ai内容全部渲染完成
                        };
                        processStream().catch((error) => {
                            debugLogger.error('处理流数据失败:', error);
                            showLoading.value = false;
                            canWriteIpt.value = true;
                            taskmesssageList.value.pop();
                            taskmesssageList.value.push({
                                role: 'assistant',
                                content:
                                    '当前请求网络可能有问题，请重新发起对话',
                                taskmessage_id: '',
                                thinkingContent: '', // 添加思考内容字段
                                thinkingCompleted: false, // 添加 thinkingCompleted 字段
                                message_comment: {},
                            });
                            scrollToBottom();
                        });
                    } else {
                        throw new Error(
                            'Invalid response format for stream mode'
                        );
                    }
                } else {
                    canWriteIpt.value = true;
                    // 正常请求
                    if (response.data.error == '1') {
                        ElMessage.error('应用不存在');
                    } else if (response.data.error == '0') {
                        // 等待中消失
                        showLoading.value = false;
                        isRendering.value = false; //ai内容全部渲染完成
                        const { topic_id, content, taskmessage_id } =
                            response.data;
                        taskmesssageList.value.push({
                            role: 'assistant',
                            content: content,
                            taskmessage_id: taskmessage_id,
                            // thinkingContent: '', // 添加思考内容字段
                            // thinkingCompleted: false, // 添加 thinkingCompleted 字段
                            // message_comment: {},
                        });
                        selectedTopicId.value = topic_id;
                        // 更新用户消息的 taskmessage_id
                        userMessageObj.taskmessage_id = taskmessage_id;
                        scrollToBottom();
                        // 可以展示操作按钮
                        showOperate.value = true;
                    }
                }
            } catch (error) {
                // ElMessage.error('网络错误');
                aiMessage.content = '当前请求网络可能有问题，请重新发起对话';
                taskmesssageList.value = [...taskmesssageList.value];
                showTool.value = false;
                userContent.value = ''; // 清空输入框
                showLoading.value = false;
                showOperate.value = false; //将最后一条聊天内容的操作按钮隐藏，等聊天内容全展示后，在将按钮展示
                canWriteIpt.value = false; //不能输入内容
                isRendering.value = true;
                showPreview.value = false; //工作台隐藏预览
                clearWorkbench(); //清空工作台
                focusIpt.value = false; //失去焦点
            }
        }
    } else if (response.message) {
        ElMessage.error(response.message);
    }
};

// 监听userContent的变化,如果输入框有文字，按钮背景色修改
watch(userContent, (newValue) => {
    if (newValue !== '') {
        canSendMessage.value = true;
    } else {
        canSendMessage.value = false;
    }
});

const handleWheel = (event) => {
    const containerDom = scrollContainer.value;
    if (containerDom) {
        if (event.deltaY < 0) {
            //鼠标滚动上滑
            canScrollToBottom.value = false;
        }
    }
};
// 在获取所有聊天记录时，滚动条一直置于底部
const scrollToBottom = () => {
    nextTick(() => {
        const containerDom = scrollContainer.value;
        if (containerDom) {
            containerDom.addEventListener('wheel', handleWheel);
            if (canScrollToBottom.value) {
                containerDom.scrollTop = containerDom.scrollHeight;
            }
        }
        // 用户发送消息后，输入框自动聚焦
        if (inputTxtRef.value) {
            // 重新聚焦输入框
            inputTxtRef.value.focus();
        } else {
            debugLogger.error('inputTxtRef is null');
        }
    });
};

// 监听页面滑动事件
const isCommonTopVisible = ref(true); // 控制container-queryapp-div的显示和隐藏
let lastScrollPosition = 0; // 存储上一次的滚动位置
const handleScroll = () => {
    const currentScrollPosition = scrollContainer.value.scrollTop;
    // 使用绝对值判断
    if (Math.abs(currentScrollPosition - lastScrollPosition) > 1) {
        if (lastScrollPosition == 0) {
            isCommonTopVisible.value = true;
        } else {
            if (currentScrollPosition > lastScrollPosition) {
                // 向下滚动
                isCommonTopVisible.value = false;
            } else {
                // 向上滚动
                isCommonTopVisible.value = true;
            }
        }
    }
    lastScrollPosition = currentScrollPosition; // 更新上一次的滚动位置
};

// 复制文本
const copyContent = (content) => {
    try {
        var p = /([\n\r])+/g;
        const textContent = content.replace(p, '\n'); //将文本中连续的一个或多个换行直接替换成一个换行，防止出现页面中有空行的情况

        // 创建一个临时的 textarea 元素
        const textarea = document.createElement('textarea');
        textarea.value = textContent;
        document.body.appendChild(textarea);

        // 选择并复制内容
        textarea.select();
        document.execCommand('copy');

        // 移除临时的 textarea 元素
        document.body.removeChild(textarea);

        ElMessage.success('内容已复制');
    } catch (err) {
        ElMessage.error('复制失败');
    }
};

// 获取topic的所有上下文内容
// 创建一个计算属性来获取 topic_id
const topicId = computed(() => route.query.topic_id);

// 监听计算属性
watch(topicId, (newVal) => {
    if (newVal) {
        selectedTopicId.value = newVal;
        fetchTaskmessages();
    }
});
const loading = ref(null);
const fetchTaskmessages = async () => {
    loading.value = ElLoading.service({
        lock: true,
        text: '加载中',
        background: 'rgba(0, 0, 0, 0.5)',
    });
    taskmesssageList.value = [];
    const response = await getTaskmessages(route.query.topic_id);
    loading.value.close();
    // 使用 for...of 循环来支持 await
    for (const item of response.data) {
        const userMessageObj = {
            role: 'user',
            content: item.input_content,
            taskmessage_id: item.id,
            create_time: item.create_time,
            favorite: item.favorite,
        };
        taskmesssageList.value.push(userMessageObj);

        const aiMessageObj = {
            role: 'assistant',
            content: '',
            taskmessage_id: item.id,
            create_time: item.create_time,
            thinkingContent: '',
            thinkingCompleted: false,
            message_comment: item.message_comment ? item.message_comment : {},
        };
        taskmesssageList.value.push(aiMessageObj);

        try {
            const outputData = JSON.parse(item.output_content);
            if (outputData) {
                // 使用 Promise.all 等待所有组件处理完成
                await Promise.all(
                    outputData.map(async (outputItem) => {
                        if (outputItem.uid !== 'default') {
                            // 创建一个 Promise 来处理预览消息
                            await new Promise((resolve) => {
                                handlePreviewMessage(outputItem);
                                // 给一个短暂的延时确保处理完成
                                setTimeout(resolve, 100);
                            });
                        } else {
                            aiMessageObj.content = outputItem.data;
                        }
                    })
                );
            } else {
                aiMessageObj.content = '暂无回答';
            }
        } catch (e) {
            aiMessageObj.content = item.output_content;
            // console.error('解析消息内容失败:', e);
        }
    }
    clearWorkbench();
    canScrollToBottom.value = true; //可以执行scrollToBottom函数
    scrollToBottom();
};

onMounted(async () => {
    console.log('IndChat onMounted 开始执行');

    // 如果有 title 参数，设置页面标题
    if (route.query.title) {
        try {
            const decodedTitle = decodeURIComponent(route.query.title);
            setPageTitle(decodedTitle);
        } catch (e) {
            debugLogger.error('标题解码失败:', e);
            setPageTitle(route.query.title);
        }
    }

    // 获取topic的所有上下文内容
    if (route.query.topic_id) {
        console.log('有topic_id，开始获取消息');
        fetchTaskmessages();
    }

    // 如果有 inputMsg 则直接发送消息,不展示欢迎语和推荐
    if (inputMsg.value) {
        console.log('有inputMsg，直接发送消息');
        userContent.value = inputMsg.value;
        sendTaskmessageHandler();
    } else if (route.query.content) {
        console.log('有content参数');
        userContent.value = decodeURIComponent(route.query.content);
    } else {
        console.log('开始获取用户信息');
        if (!userInfo.value) {
            try {
                await userStore.fetchUserInfo();
                console.log('用户信息获取成功:', userInfo.value);
            } catch (error) {
                console.error('用户信息获取失败:', error);
            }
        }
        //1.5秒后文字逐个展示
        console.log('设置定时器显示标题');
        setTimeout(() => {
            console.log('开始显示标题1');
            showTitle1();
        }, 1000);
    }

    // 组件挂载后，获取滚动容器并添加滚动事件监听
    if (scrollContainer.value) {
        scrollContainer.value.addEventListener('scroll', handleScroll);
    }

    // 初始化消息总线
    const { onMessage } = useMessageBus();

    // 监听预览渲染消息
    onMessage(MessageType.TRIGGER_PREVIEW, handlePreviewMessageCallback);

    // 监听工作台渲染消息
    onMessage(MessageType.TRIGGER_WORKBENCH, handleWorkbenchMessageCallback);
});

onUnmounted(() => {
    // 组件卸载前，移除滚动事件监听
    if (scrollContainer.value) {
        scrollContainer.value.removeEventListener('scroll', handleScroll);
    }

    // 移除消息监听
    const { offMessage } = useMessageBus();
    offMessage(MessageType.TRIGGER_PREVIEW, handlePreviewMessageCallback);
    offMessage(MessageType.TRIGGER_WORKBENCH, handleWorkbenchMessageCallback);
});

// 模拟组件内容输出格式

const { updatePreviewComponents } = usePreviewComponents();

// 用于收集同一批次的预览消息
const previewMessagesBatch = ref([]);
// 批处理定时器
let batchUpdateTimer = null;
// 右侧组件内容
const rightBottomComponent = shallowRef(null);
// 右侧组件key
const rightBottomComponentKey = ref(null);
// 右侧组件数据
const rightBottomData = ref(null);

const handlePreviewMessageCallback = (message) => {
    handlePreviewMessage(message);
};

const handleWorkbenchMessageCallback = ({ uid, data }) => {
    debugLogger.log('收到工作台消息:', { uid, data });

    Promise.resolve()
        .then(() => {
            rightBottomComponent.value = resolveComponent(uid);
            rightBottomComponentKey.value = `${uid}-${Date.now()}`;
            rightBottomData.value = data;

            return nextTick();
        })
        .then(() => {
            debugLogger.log('工作台状态更新:', {
                component: rightBottomComponent.value,
                rightBottomData: rightBottomData.value,
            });
        });
};

const handlePreviewMessage = (message) => {
    if (message.uid === 'file-list-component') {
        debugLogger.log('收到文件列表消息:', message);
    }

    previewMessagesBatch.value.push(message);

    if (batchUpdateTimer) {
        clearTimeout(batchUpdateTimer);
    }

    batchUpdateTimer = setTimeout(() => {
        const updatedMessages = updatePreviewComponents(
            previewMessagesBatch.value
        );

        const lastMessage =
            taskmesssageList.value[taskmesssageList.value.length - 1];
        if (lastMessage && lastMessage.role === 'assistant') {
            updatedMessages.forEach((msg) => {
                if (
                    msg.area === 'mid-txt-bottom' &&
                    msg.uid === 'file-list-component'
                ) {
                    debugLogger.log('文件列表组件更新:', {
                        组件名: msg.component,
                        组件Key: msg.componentKey,
                    });
                }

                if (msg.area === 'mid-def-top') {
                    lastMessage.topComponent = msg.component;
                    lastMessage.topComponentKey = msg.componentKey;
                    lastMessage.topData = msg.data;
                } else if (msg.area === 'mid-def-bottom') {
                    lastMessage.bottomComponent = msg.component;
                    lastMessage.bottomComponentKey = msg.componentKey;
                    lastMessage.bottomData = msg.data;
                } else if (msg.area === 'mid-txt-bottom') {
                    lastMessage.txtBottomComponent = msg.component;
                    lastMessage.txtBottomComponentKey = msg.componentKey;
                    lastMessage.txtBottomData = msg.data;
                } else if (msg.area === 'right-bottom') {
                    rightBottomComponent.value = msg.component;
                    rightBottomComponentKey.value = msg.componentKey;
                    rightBottomData.value = msg.data;
                }
            });

            taskmesssageList.value = [...taskmesssageList.value];

            nextTick(() => {
                if (lastMessage.txtBottomComponent === 'file-list-component') {
                    debugLogger.log('DOM更新后文件列表组件状态:', {
                        是否存在组件: !!lastMessage.txtBottomComponent,
                        组件数据: lastMessage.txtBottomData,
                    });
                }

                setTimeout(() => {
                    if (canScrollToBottom.value) {
                        scrollToBottom();
                    }
                }, 100);
            });
        }

        previewMessagesBatch.value = [];
        batchUpdateTimer = null;
    }, 0);
};

// 点赞，点踩操作
const sendCommentVote = debounce(async (message, vote) => {
    console.log(message, '----点赞');
    try {
        const response = await addMessageCommentApi(
            message.taskmessage_id,
            vote
        );
        if (response.data.error == '0') {
            message.message_comment.vote = vote;
        } else {
            ElMessage.error(response.data.message);
        }
    } catch (e) {
        ElMessage.error('操作错误', e);
    }
}, 100);

// 点踩
const delCommentVote = debounce(async (message) => {
    // console.log(message, '----点踩');

    try {
        const response = await delMessageCommentApi(message.taskmessage_id);
        // console.log(response);
        if (response.data.error == '0') {
            message.message_comment.vote = '';
        } else {
            ElMessage.error(response.data.message);
        }
    } catch (e) {
        ElMessage.error('操作错误', e);
    }
}, 100);

// 收藏指令
const handleFavorite = debounce(async (message, vote) => {
    // 设置加载状态
    message.favoriteLoading = true;

    try {
        if (vote == '0') {
            const response = await delFavoriteApi(message.favorite.id);
            if (response.data.error == '0') {
                message.favorite = null;
            } else {
                ElMessage.error(response.data.message);
            }
        } else {
            const response = await addFavoriteApi(
                message.taskmessage_id,
                message.content,
                route.query.app_id || ''
            );
            if (response.data.error == '0') {
                message.favorite = {
                    id: response.data.favorite_id,
                };
            } else {
                ElMessage.error(response.data.message);
            }
        }
    } catch (e) {
        ElMessage.error('操作错误', e);
    } finally {
        // 无论成功失败都取消加载状态
        message.favoriteLoading = false;
    }
}, 100);
</script>
<style scoped>
@import url('@/assets/styles/chat/index.css');
.taskmessage-add > div {
    padding: 15px 20px;
    min-height: 116px;
}
:deep(.el-textarea__inner) {
    padding: 0;
}

/* 解决 markdown 渲染后底部多余高度问题 */
:deep(.markdown-content) {
    display: block;
}

:deep(.markdown-content > *:last-child) {
    margin-bottom: 0;
    padding-bottom: 0;
}

:deep(.markdown-content p) {
    margin: 0;
    padding: 0;
}

:deep(.markdown-content p:last-child) {
    margin: 0;
    padding: 0;
    line-height: 1;
}

/* 用户消息特定处理 */
:deep(.user-message) {
    line-height: 30px;
    display: inline-block;
}

:deep(.user-message p) {
    display: inline;
    margin: 0;
    padding: 0;
    line-height: inherit;
}
</style>
