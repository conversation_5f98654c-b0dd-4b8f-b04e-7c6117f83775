/* 工作台 - 左侧固定 */
.el-main-left {
    width: 800px;
    flex-shrink: 0;
    padding: 35px 20px;
    box-shadow: 4px 1px 15px 1px rgba(0, 0, 0, 0.08);
    border-radius: 0px 30px 35px 0px;
}

/* 工作台标题样式 */
.el-main-left-top {
    padding-bottom: 20px;
    border-bottom: 1px solid #e5e5e5;
}

/* 聊天区域 - 右侧固定 */
.el-main-right {
    flex: 1;
    padding: 0 32px;
}

.layout-nav {
    position: absolute;
    top: 50%;
    left: 53%;
    transform: translate(-50%, -50%);
    background: linear-gradient(
        129deg,
        rgba(18, 155, 255, 0.1) 0%,
        rgba(62, 96, 233, 0.15) 100%
    );
    box-shadow:
        0px 13px 17px 0px rgba(0, 19, 65, 0.05),
        inset 0px 1px 1px 0px #ffffff;
    border-radius: 21px;
    width: 80px;
    padding: 15px;
}
.layout-nav-hot {
    position: absolute;
    top: 50%;
    right: 0;
    cursor: pointer;
    padding: 0 3px;
}
.layout-nav-line {
    width: 3px;
    height: 20px;
    background: #d9d9d9;
    transition:
        transform 0.1s ease,
        background-color 0.1s ease;
}
.layout-nav-hot:hover .layout-nav-line {
    background: #129bff;
}
.layout-nav-line.left:nth-child(1) {
    transform-origin: 50% 0%;
    transform: rotate(17deg) translateY(1px);
}
.layout-nav-line.left:nth-child(2) {
    transform-origin: 50% 100%;
    transform: rotate(-17deg) translateY(-1px);
}
.layout-nav-line.right:nth-child(1) {
    transform-origin: 50% 100%;
    transform: rotate(-17deg) translateY(1px);
}
.layout-nav-line.right:nth-child(2) {
    transform-origin: 50% 0%;
    transform: rotate(17deg) translateY(-1px);
}
.el-slide-active1 {
    width: 50px;
    transform: translateX(-20px); /* 隐藏 */
}
.el-slide-active1 .layout-nav {
    display: none;
}
.el-slide-active2 {
    transform: translateX(0); /* 显示 */
}

.layout-nav-item1 {
    padding-bottom: 10px;
}
.layout-nav-item2 {
    padding: 30px 0;
}
.layout-nav-item3 {
    padding-top: 10px;
    cursor: pointer;
}
.siteuser-headimg {
    width: 50px;
    height: 50px;
    line-height: 50px;
    background: linear-gradient(157deg, #5f6c90 0%, #393d49 100%);
    border-radius: 50%;
    text-align: center;
    color: #fff;
    flex-shrink: 0;
}
.layout-nav-item2-item {
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 15px;
    background: rgba(255, 255, 255, 0.42);
    margin-bottom: 30px;
}
.layout-nav-item2-item:hover {
    background: #cee7ff;
}
.layout-nav-item2 .layout-nav-item2-item:last-child {
    margin-bottom: 0;
}
.layout-nav-item2-item img {
    width: 22px;
    height: 22px;
}
.layout-nav-item2-item-active {
    background: rgba(18, 155, 255, 0.42);
}
.container-message {
    width: 800px;
    margin: 0 auto;
    height: 100%;
    display: flex;
    padding: 0px 30px 50px;
    position: relative;
    overflow: hidden;
}

.container-message-left {
    position: relative;
    width: 100%;
    padding: 35px 0 45px;
}
.container-top-mengban {
    height: 30px;
    background: linear-gradient(
        180deg,
        #f5fbff 0%,
        rgb(245 251 255 / 72%) 45%,
        rgba(244, 250, 255, 0) 100%
    );
    backdrop-filter: blur(0px);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 2;
}
.container-queryapp-div {
    position: absolute;
    top: 25px;
    left: 0;
    right: 0;
    width: 170px;
    height: 45px;
    border-radius: 15px;
    background: linear-gradient(129deg, #cee8ff 0%, #d6e0fb 100%);
    margin: 0 auto;
    z-index: 2;
}
.container-queryapp-nav {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 20px;
    cursor: pointer;
}
.container-queryapp-nav span {
    font-weight: bold;
    margin-right: 5px;
}
.container-queryapp-list {
    position: absolute;
    top: 65px;
    left: 0;
    padding: 20px;
    background: linear-gradient(139deg, #ecf7ff 0%, #eff2fd 100%);
    border-radius: 15px;
    z-index: 2;
    width: max-content;
    max-height: 500px;
}
.container-queryapp-list > div:nth-child(1) {
    color: #666;
    font-weight: bold;
    margin-bottom: 10px;
}
.container-queryapp-item {
    padding: 12px 15px;
    margin-bottom: 10px;
    min-width: 220px;
    max-width: 350px;
    display: flex;
    cursor: pointer;
    border-radius: 10px;
}
.container-queryapp-item-img {
    mask: url(@/assets/index1/queryapp.png);
    -webkit-mask: url(@/assets/index1/queryapp.png) no-repeat center center;
    mask-size: contain;
    -webkit-mask-size: contain;
    width: 16px;
    height: 16px;
    background-color: #666;
    margin-right: 5px;
}
.container-queryapp-item-title {
    color: #666;
}
.container-queryapp-item:hover .container-queryapp-item-img {
    background-image: linear-gradient(129deg, #129bff 0%, #3e60e9 100%);
}
.container-queryapp-item:hover .container-queryapp-item-title {
    color: #000;
    font-weight: bold;
}
.container-queryapp-item:hover {
    background: #fff;
}
.conatiner-tips-div {
    position: absolute;
    bottom: 15px;
    left: 0;
    right: 0;
    margin: auto;
    color: #d9d9d9;
    text-align: center;
}
.container-center {
    height: 100%;
    width: 100%;
}
.conatiner-message-div {
    height: 100%;
    width: 100%;
    position: relative;
}
.taskmessage-add {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
}
.taskmessage-add > div {
    width: 100%;
    min-height: 160px;
    box-shadow: 0px 15px 13px -4px rgba(0, 0, 0, 0.1);
    background: #ffffff;
    border-radius: 20px;
    border: 1px solid #129bff;
    overflow: hidden;
    padding: 15px;
}
.taskmessage-add > div.taskmessage-ipt-no {
    border-color: #d9d9d9;
}
.taskmessage-add button {
    width: 40px;
    height: 40px;
    background: #129bff;
    border: none;
    border-radius: 10px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}
.taskmessage-add .send-btn-no {
    background: #f5f5f5;
}
.taskmessage-add button img {
    width: 20px;
    height: 20px;
    object-fit: contain;
    object-position: center;
    position: relative;
    top: -2px;
    left: -1px;
}
.taskmessage-div {
    height: 100%;
    position: relative;
    padding-bottom: 25px;
}
@font-face {
    font-family: 'PingFang XT';
    src: url('@/assets/fonts/PingFangXiTi.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

.opening-statement-div {
    position: absolute;
    top: 50%;
    left: 50%;
    padding: 0 0 165px;
    transform: translate(-50%, -50%);
    width: 100%;
}

.opening-statement-div > div:nth-child(1) {
    font-size: 48px;
    line-height: 67px;
    margin-bottom: 15px;
}
.opening-statement-div > div:nth-child(2) {
    font-size: 36px;
    line-height: 50px;
}
.chat-item {
    display: flex;
    margin-bottom: 25px;
}
.chat-item-ai-headimg-div {
    width: 50px;
    height: 50px;
    border-radius: 15px;
    background: linear-gradient(129deg, #129bff 0%, #3e60e9 100%);
    flex-shrink: 0;
    display: flex;
    justify-content: center;
    align-items: center;
}
.chat-item-ai-headimg {
    width: 50px;
    height: 50px;
    flex-shrink: 0;
}
.chat-item-content {
    border-radius: 15px;
    margin-left: 10px;
}
.opening-statement {
    padding: 25px;
}
.taskmessage-ai-content,
.opening-statement {
    background: #fff;
    width: 94%;
}
.taskmessage-ai-content {
    padding: 18px 25px 15px 25px;
}
.opening-statement-title {
    font-weight: bold;
}
.opening-statement-desc {
    color: #666;
    margin-top: 12px;
}
.taskmessage-user-content {
    padding: 15px 25px 0;
    background: linear-gradient(
        129deg,
        rgba(18, 155, 255, 0.07) 0%,
        rgba(62, 96, 233, 0.07) 100%
    );
    position: relative;
    min-width: 130px;
    white-space: pre-wrap;
}
.chat-item-top-operate {
    position: absolute;
    bottom: -20px;
    right: 0;
    border-radius: 10px;
    border: 1px solid #d8d8d8;
    overflow: hidden;
    background: #fff;
    display: none;
}
.taskmessage-user-content:hover .chat-item-top-operate {
    display: block;
}
.chat-item-top-operate-ai {
    margin-top: 10px;
}
.chat-item-top-operate-ai .el-button {
    border: none;
    box-shadow: none;
    padding: 1px;
    height: 20px;
}
.chat-item-top-operate .el-button {
    margin: 0;
    border: none;
    height: 33px;
    width: 40px;
    border-radius: 0;
}
.chat-item-top-operate .el-button + .el-button {
    margin: 0;
    border: none;
    border-left: 1px solid #d8d8d8;
}
.el-button:hover {
    background: #eeeeee !important;
    color: #666 !important;
}
/* 发送内容 */
:deep(.el-textarea) {
    width: 100%;
    font-size: 16px;
}
:deep(.el-textarea__inner:focus),
:deep(.el-textarea__inner),
:deep(.el-textarea__inner:hover) {
    box-shadow: none;
    -ms-overflow-style: none;
    scrollbar-width: none;
}
:deep(.el-textarea.is-disabled .el-textarea__inner) {
    box-shadow: none;
    background: none;
}

@media (max-width: 1000px) {
    .container {
        width: 80%;
    }
}

.chat-input-box button {
    width: 40px;
    height: 40px;
    background: #129bff;
    border: none;
    border-radius: 10px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}
.chat-input-box .send-btn-no {
    background: #f5f5f5;
}
.chat-input-box button img {
    width: 20px;
    height: 20px;
    object-fit: contain;
    object-position: center;
}

.chat-input-box {
    display: flex;
    align-items: stretch;
    justify-content: space-between;
    padding: 10px 25px 10px 20px;
    min-height: 72px;
    height: auto;
    box-shadow: 0 15px 13px -4px rgba(0, 0, 0, 0.1);
    border-radius: 20px;
    border: 1px solid #129bff;
    background: #fff;
}
